import matplotlib.pyplot as plt
import streamlit as st

from backend.api.handler_v2 import predict_task
from src.game.visualization.visualize_game_state import visualize_state


def Col1(game, temp_path):
    st.header("🎮 Map gốc")
    prediction = predict_task(temp_path)
    st.write(f"Độ khó dự đoán: {prediction}")

    # Hiển thị thông tin level
    st.info(f"""
    **Level:** {game.level}  
    **Tên:** {game.name}  
    **Đ<PERSON> khó:** {game.difficulty}  
    **Kích thước:** {game.size_x} x {game.size_y}  
    **Số blocks:** {len(game.blocks)}  
    **Số doors:** {len(game.doors)}
    """)

    # Vẽ trạng thái ban đầu
    fig, ax = plt.subplots(figsize=(10, 10))
    visualize_state(game, ax=ax)
    st.pyplot(fig)
    plt.close()
