import requests
import streamlit as st

from frontend.utils.const import BACKEND_URL, BASIC_AUTH_PASSWORD, BASIC_AUTH_USERNAME
from frontend.utils.logger import logger
from src.visualization.streamlit_style_generator import StreamlitStyleGenerator


class SolveResultHandler:
    def __init__(self):
        self.auth = (BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD)

    def handle_random_result(self, result, game, task_id, wait_time):
        """Xử lý kết quả mode random"""
        if result.get("solvable", False):
            path_length = result.get("path_length", 0)
            st.success(f"""
                    ✅ **Level có thể giải được!**  
                    📏 Độ dài path: {path_length} moves  
                    ⚡ Thời gian: {wait_time}s
                    """)
            
            # Tạo và hiển thị các ảnh random
            try:
                with st.spinner("Đang tạo visualization cho random algorithm..."):
                    path = result.get("path", [])
                    if path:
                        visualizer = StreamlitStyleGenerator(game, task_id)
                        viz_result = visualizer.create_random_images(path, is_solvable=True)

                        if viz_result["success"]:
                            blocks_eaten = viz_result["total_blocks_eaten"]
                            block_ids = [state["block_id"] for state in viz_result["eat_states"]]

                            st.info(f"""
                                    **Phân tích Random Algorithm:**
                                    - Số blocks ăn được: {blocks_eaten}
                                    - Thứ tự ăn: {block_ids}
                                    """)

                            # Hiển thị ảnh theo grid 2 cột
                            images = viz_result["images"]
                            if len(images) > 0:
                                cols = st.columns(2)
                                for idx, img_path in enumerate(images):
                                    with cols[idx % 2]:
                                        st.image(img_path, width=1000)
                        else:
                            st.warning(f"⚠️ Không tạo được visualization: {viz_result.get('error', 'Unknown error')}")

            except Exception as viz_error:
                st.warning(f"Không tạo được visualization: {str(viz_error)}")
        else:
            st.error(f"""
                    ❌ **Level không thể giải được**  
                    ⚡ Thời gian check: {wait_time}s
                    """)
            
            # Hiển thị final state cho trường hợp không giải được
            try:
                with st.spinner("Đang tạo final state visualization..."):
                    path = result.get("path", [])
                    error_block_id = result.get("error_block_id")
                    visualizer = StreamlitStyleGenerator(game, task_id)
                    viz_result = visualizer.create_random_images(
                        path, is_solvable=False, error_block_id=error_block_id
                    )
                    if viz_result["success"] and viz_result["images"]:
                        st.image(viz_result["images"][-1], width=1000)
            except Exception as viz_error:
                st.warning(f"Không tạo được final state: {str(viz_error)}")

    def handle_heuristic_result(self, result, solve_mode, uploaded_file, task_id, wait_time):
        """Xử lý kết quả các mode heuristic"""
        path = result.get("path", [])
        vis_info = result.get("visualization", {})

        if path and len(path) > 0:
            # Thông tin comparison cho mode check_then_solve
            comparison_text = ""
            if solve_mode == "check_then_solve" and "random_path_length" in result:
                improvement = result.get("improvement", 0)
                comparison_text = f"""
                        📊 **So sánh:**
                        - Random: {result["random_path_length"]} moves
                        - Heuristic: {result["heuristic_path_length"]} moves  
                        - Cải thiện: {improvement} moves ({improvement / result["random_path_length"] * 100:.1f}%)
                        """

            # Thông tin cho order_block
            order_info = ""
            if solve_mode == "order_block":
                used_order = result.get("order_used", [])
                order_info = f"""
                        🔢 **Thứ tự blocks sử dụng:** {used_order}
                        """

            # Tên thuật toán
            algorithm_name = {
                "heuristic_only": "Heuristic",
                "medium_path_only": "Medium Path",
                "check_then_solve": "Heuristic tối ưu",
                "order_block": "Order Block Solver",
            }.get(solve_mode, "Unknown")

            st.success(f"""
                    ✅ **Đã tìm thấy lời giải với {algorithm_name}!**  
                    📏 Độ dài path: {len(path)} moves  
                    🎬 Video: {"Có" if vis_info.get("success") else "Không"}  
                    ⚡ Thời gian: {wait_time}s
                    {comparison_text}
                    {order_info}
                    """)

            # Hiển thị video và download
            if vis_info.get("success"):
                self._show_video_and_downloads(task_id, uploaded_file, solve_mode)
        else:
            # Không có path hợp lệ
            if result.get("fallback_to_random"):
                st.warning(f"""
                        ⚠️ **Heuristic không thành công, nhưng random đã giải được:**  
                        📏 Random path: {result.get("random_path_length", "N/A")} moves  
                        🔧 Heuristic solver có vấn đề  
                        ⚡ Thời gian: {wait_time}s
                        """)
            else:
                st.error(f"""
                        ❌ **Thuật toán không tìm thấy lời giải**  
                        ⚡ Thời gian: {wait_time}s  
                        💡 Thử mode "Kiểm tra nhanh" để xác nhận level có giải được không
                        """)

    def _show_video_and_downloads(self, task_id, uploaded_file, solve_mode):
        """Hiển thị video và nút download"""
        col_vid1, col_vid2 = st.columns(2)

        with col_vid1:
            st.subheader("🎬 Video Demo")
            try:
                logger.info(f"🎬 Gửi request video - Task ID: {task_id}")
                video_response = requests.get(
                    f"{BACKEND_URL}/video/{task_id}",
                    auth=self.auth,
                )
                if video_response.status_code == 200:
                    st.video(video_response.content)
                    st.download_button(
                        label="📥 Download MP4",
                        data=video_response.content,
                        file_name=f"{uploaded_file.name.replace('.json', '')}_solution_{solve_mode}.mp4",
                        mime="video/mp4",
                    )
            except Exception as e:
                st.warning(f"Không tải được video: {str(e)}")

        with col_vid2:
            st.subheader("📦 Download Files")
            try:
                logger.info(f"📦 Gửi request zip - Task ID: {task_id}")
                zip_response = requests.get(
                    f"{BACKEND_URL}/zip/{task_id}",
                    auth=self.auth,
                )
                if zip_response.status_code == 200:
                    st.download_button(
                        label="📥 Download All (Ảnh + Video)",
                        data=zip_response.content,
                        file_name=f"{uploaded_file.name.replace('.json', '')}_complete_{solve_mode}.zip",
                        mime="application/zip",
                    )
                    st.success("✅ Ảnh từng bước và video đã sẵn sàng!")
            except Exception as e:
                st.warning(f"Không tải được zip: {str(e)}")