import json
import os
import tempfile

import dotenv
import streamlit as st

from frontend.components.column1 import Col1
from frontend.components.column2 import Col2
from frontend.components.sidebar import Sidebar
from frontend.components.user_manual import UserManual
from src.game.game_state_base import GameStateBase as GameState

dotenv.load_dotenv()


def solver_screen():
    st.set_page_config(page_title="SlideJam Solver Visualizer", page_icon="🧩", layout="wide")

    st.title("🧩 SlideJam x AI")
    st.markdown("Upload một file JSON level để giải puzzle hoặc sinh map mới!")

    # THÊM: KHỞI TẠO SESSION STATE
    if "current_task_id" not in st.session_state:
        st.session_state.current_task_id = None
    if "task_running" not in st.session_state:
        st.session_state.task_running = False
    if "cancel_task" not in st.session_state:
        st.session_state.cancel_task = False
    if "task_mode" not in st.session_state:
        st.session_state.task_mode = None

    uploaded_file, main_mode, order_input, solve_mode, auto_solve, auto_gen, mode, num_blocks = (
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
    )

    # Sidebar cho upload file
    uploaded_file, main_mode, order_input, solve_mode, auto_solve, auto_gen, mode, num_blocks = (
        Sidebar(
            uploaded_file=uploaded_file,
            main_mode=main_mode,
            order_input=order_input,
            solve_mode=solve_mode,
            auto_solve=auto_solve,
            auto_gen=auto_gen,
            mode=mode,
            num_blocks=num_blocks,
        )
    )
    # Main content
    col1, col2 = st.columns([1, 1])

    if uploaded_file:
        # Đọc file JSON
        try:
            # Lưu file tạm
            uploaded_file.seek(0)  # THÊM RESET FILE POINTER
            with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as tmp_file:
                json.dump(json.load(uploaded_file), tmp_file)
                temp_path = tmp_file.name

            # Tạo GameState
            game = GameState(temp_path)

            with col1:
                Col1(game, temp_path)

            with col2:
                Col2(
                    game,
                    main_mode,
                    order_input,
                    solve_mode,
                    auto_solve,
                    auto_gen,
                    uploaded_file,
                    mode,
                    num_blocks,
                )

            # Cleanup
            os.unlink(temp_path)
        except Exception as e:
            st.error(f"Lỗi khi đọc file: {str(e)}")
    else:
        UserManual()


def login_screen():
    st.header("This app is private.")
    st.subheader("Please log in.")
    st.button("Log in with Google", on_click=st.login)


deploy_mode = os.getenv("DEPLOY_MODE", "local")

# Nếu là test thì không cần login, các mode khác thì phải login nếu user chưa login
if deploy_mode == "test":
    need_auth = False
else:
    need_auth = not st.user.is_logged_in

if need_auth:
    login_screen()
else:
    solver_screen()
    # logout in sidebar
    with st.sidebar:
        st.button("Log out", on_click=st.logout)
